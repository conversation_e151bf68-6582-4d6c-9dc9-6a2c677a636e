@import 'ant-design-vue/lib/button/style/index.less';

@btn-shadow: none;
@btn-primary-shadow: none;
@btn-text-shadow: none;
@btn-font-size-sm: @font-size-base;
@btn-padding-base: 0 12px;
@btn-primary-color: @kys-color-white;
@btn-danger-color: @kys-color-red-500;
@btn-danger-border: @kys-color-red-500;
@btn-success-color: @kys-color-green-500;
@btn-success-border: @kys-color-green-500;
@btn-height-base: 32px;
@btn-height-lg: 40px;
@btn-height-sm: 24px;

.button-theme(@namespace, @front-color, @background-color, @border-color) {
  &-@{namespace} {
    color: @front-color;
    background-color: @background-color;
    border-color: @border-color;
  }
}
.button-theme-hover(@namespace, @front-color, @background-color, @border-color) {
  &-@{namespace} {
    &:hover,
    &:focus {
      color: @front-color;
      background-color: @background-color;
      border-color: @border-color;
    }
  }
}
.button-theme-active(@namespace, @front-color, @background-color, @border-color) {
  &-@{namespace} {
    &.active {
      color: @front-color;
      background-color: @background-color;
      border-color: @background-color;
    }
  }
}
.button-theme-ghost(@namespace, @front-color, @border-color) {
  &-background-ghost&-@{namespace} {
    color: @front-color;
    border-color: @border-color;
    background: transparent;
    &:hover {
      color: @front-color;
      border-color: @border-color;
    }
  }
}
.button-theme-disable(@namespace, @front-color: @kys-color-black-200, @background-color: #fafafa, @border-color: @kys-color-gray-700) {
  &-@{namespace} {
    &-disabled,
    &.disabled,
    &[disabled] {
      &,
      &:hover,
      &:focus,
      &:active,
      &.active {
        color: @front-color;
        background-color: @background-color;
        border-color: @border-color;
        // color: if(isdefined(@front-color), @front-color, @kys-color-black-200);
        // background-color: if(isdefined(@background-color), @background-color, #fafafa);
        // border-color: if(isdefined(@border-color), @border-color, @kys-color-gray-700);
      }
    }
  }
}


.ant-btn {
  text-shadow: none; // 移除文本阴影
  box-shadow: none; // 移除按钮阴影

  &::after {
    animation: none; // 移除水波纹
  }

  & > .anticon + span,
  & > span + .anticon {
    margin-left: 4px;
  }

  // Size
  // --------------------
  padding: @btn-padding-base;
  min-height: @btn-height-base;

  &-sm {
    padding: 0 8px;
    min-height: @btn-height-sm;
  }
  &-lg {
    padding: 0 16px;
    min-height: @btn-height-lg;
  }
}
